{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/assets/assets.ts"], "sourcesContent": ["export const assets = {\n  add_icon: '/assets/add_icon.svg',\n  admin_logo: '/assets/admin_logo.svg',\n  appointment_icon: '/assets/appointment_icon.svg',\n  cancel_icon: '/assets/cancel_icon.svg',\n  doctor_icon: '/assets/doctor_icon.svg',\n  upload_area: '/assets/upload_area.svg',\n  home_icon: '/assets/home_icon.svg',\n  patients_icon: '/assets/patients_icon.svg',\n  people_icon: '/assets/people_icon.svg',\n  list_icon: '/assets/list_icon.svg',\n  tick_icon: '/assets/tick_icon.svg',\n  appointments_icon: '/assets/appointments_icon.svg',\n  earning_icon: '/assets/earning_icon.svg',\n};\n"], "names": [], "mappings": ";;;;AAAO,MAAM,SAAS;IACpB,UAAU;IACV,YAAY;IACZ,kBAAkB;IAClB,aAAa;IACb,aAAa;IACb,aAAa;IACb,WAAW;IACX,eAAe;IACf,aAAa;IACb,WAAW;IACX,WAAW;IACX,mBAAmB;IACnB,cAAc;AAChB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext } from 'react';\nimport { assets } from '@/assets/assets';\nimport { AdminContext } from '@/context/AdminContext';\nimport { useRouter } from 'next/navigation';\nimport { ConsultantContext } from '@/context/ConsultantContext';\n\nconst Navbar: React.FC = () => {\n  const adminContext = useContext(AdminContext);\n  const consultantContext = useContext(ConsultantContext);\n  const router = useRouter();\n\n  if (!adminContext || !consultantContext) {\n    return null;\n  }\n\n  const { aToken, setAToken } = adminContext;\n  const { dToken, setDToken } = consultantContext;\n\n  const logout = () => {\n    router.push('/');\n    if (aToken) {\n      setAToken('');\n      localStorage.removeItem('aToken');\n    }\n    if (dToken) {\n      setDToken('');\n      localStorage.removeItem('dToken');\n    }\n  };\n\n  return (\n    <div className=\"flex justify-between items-center px-4 sm:px-10 py-3 border-b bg-white\">\n      <div className=\"flex items-center gap-2 text-xs\">\n        <img\n          className=\"w-36 sm:w-40 cursor-pointer\"\n          src={assets.admin_logo}\n          alt=\"Admin Logo\"\n        />\n        <p className=\"border px-2.5 py-0.5 rounded-full border-gray-500 text-gray-600\">\n          {aToken ? 'Admin' : 'Consultant'}\n        </p>\n      </div>\n      <button\n        onClick={logout}\n        className=\"bg-primary text-white text-sm px-10 py-2 rounded-full\"\n      >\n        Logout\n      </button>\n    </div>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,SAAmB;IACvB,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,oBAAoB,IAAA,mNAAU,EAAC,yJAAiB;IACtD,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QACvC,OAAO;IACT;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IAC9B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IAE9B,MAAM,SAAS;QACb,OAAO,IAAI,CAAC;QACZ,IAAI,QAAQ;YACV,UAAU;YACV,aAAa,UAAU,CAAC;QAC1B;QACA,IAAI,QAAQ;YACV,UAAU;YACV,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,KAAK,iIAAM,CAAC,UAAU;wBACtB,KAAI;;;;;;kCAEN,8OAAC;wBAAE,WAAU;kCACV,SAAS,UAAU;;;;;;;;;;;;0BAGxB,8OAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP;uCAEe", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext } from 'react';\nimport { AdminContext } from '@/context/AdminContext';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { assets } from '@/assets/assets';\nimport { ConsultantContext } from '@/context/ConsultantContext';\n\nconst Sidebar: React.FC = () => {\n  const adminContext = useContext(AdminContext);\n  const consultantContext = useContext(ConsultantContext);\n  const pathname = usePathname();\n\n  if (!adminContext || !consultantContext) {\n    return null;\n  }\n\n  const { aToken } = adminContext;\n  const { dToken } = consultantContext;\n\n  const isActive = (path: string) => pathname === path;\n\n  const linkClass = (path: string) =>\n    `flex items-center gap-3 py-3.5 px-3 md:px-9 md:min-w-72 cursor-pointer ${\n      isActive(path) ? 'bg-[#F2F3FF] border-r-4 border-primary' : ''\n    }`;\n\n  return (\n    <div className=\"min-h-screen bg-white border-r\">\n      {aToken && (\n        <ul className=\"text-[#515151] mt-5\">\n          <Link href=\"/admin-dashboard\" className={linkClass('/admin-dashboard')}>\n            <img src={assets.home_icon} alt=\"Dashboard\" />\n            <p className=\"hidden md:block\">Dashboard</p>\n          </Link>\n          <Link href=\"/all-appointments\" className={linkClass('/all-appointments')}>\n            <img src={assets.appointment_icon} alt=\"Appointments\" />\n            <p className=\"hidden md:block\">Appointments</p>\n          </Link>\n          <Link href=\"/add-consultant\" className={linkClass('/add-consultant')}>\n            <img src={assets.add_icon} alt=\"Add Consultant\" />\n            <p className=\"hidden md:block\">Add Consultant</p>\n          </Link>\n          <Link href=\"/consultant-list\" className={linkClass('/consultant-list')}>\n            <img src={assets.people_icon} alt=\"Consultant List\" />\n            <p>Consultant List</p>\n          </Link>\n        </ul>\n      )}\n      {dToken && (\n        <ul className=\"text-[#515151] mt-5\">\n          <Link href=\"/consultant-dashboard\" className={linkClass('/consultant-dashboard')}>\n            <img src={assets.home_icon} alt=\"Dashboard\" />\n            <p className=\"hidden md:block\">Dashboard</p>\n          </Link>\n          <Link href=\"/consultant-appointments\" className={linkClass('/consultant-appointments')}>\n            <img src={assets.appointment_icon} alt=\"Appointments\" />\n            <p className=\"hidden md:block\">Appointments</p>\n          </Link>\n          <Link href=\"/consultant-profile\" className={linkClass('/consultant-profile')}>\n            <img src={assets.people_icon} alt=\"Profile\" />\n            <p className=\"hidden md:block\">Profile</p>\n          </Link>\n        </ul>\n      )}\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,UAAoB;IACxB,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,oBAAoB,IAAA,mNAAU,EAAC,yJAAiB;IACtD,MAAM,WAAW,IAAA,iJAAW;IAE5B,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QACvC,OAAO;IACT;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,WAAW,CAAC,OAAiB,aAAa;IAEhD,MAAM,YAAY,CAAC,OACjB,CAAC,uEAAuE,EACtE,SAAS,QAAQ,2CAA2C,IAC5D;IAEJ,qBACE,8OAAC;QAAI,WAAU;;YACZ,wBACC,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC,uKAAI;wBAAC,MAAK;wBAAmB,WAAW,UAAU;;0CACjD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,SAAS;gCAAE,KAAI;;;;;;0CAChC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAAoB,WAAW,UAAU;;0CAClD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,gBAAgB;gCAAE,KAAI;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAAkB,WAAW,UAAU;;0CAChD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,QAAQ;gCAAE,KAAI;;;;;;0CAC/B,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAAmB,WAAW,UAAU;;0CACjD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,WAAW;gCAAE,KAAI;;;;;;0CAClC,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;YAIR,wBACC,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC,uKAAI;wBAAC,MAAK;wBAAwB,WAAW,UAAU;;0CACtD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,SAAS;gCAAE,KAAI;;;;;;0CAChC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAA2B,WAAW,UAAU;;0CACzD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,gBAAgB;gCAAE,KAAI;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAAsB,WAAW,UAAU;;0CACpD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,WAAW;gCAAE,KAAI;;;;;;0CAClC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;uCAEe", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { AdminContext } from '@/context/AdminContext';\nimport { ConsultantContext } from '@/context/ConsultantContext';\nimport Navbar from '@/components/Navbar';\nimport Sidebar from '@/components/Sidebar';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {\n  const adminContext = useContext(AdminContext);\n  const consultantContext = useContext(ConsultantContext);\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!adminContext || !consultantContext) return;\n\n    const { aToken } = adminContext;\n    const { dToken } = consultantContext;\n\n    if (!aToken && !dToken) {\n      router.push('/login');\n    }\n  }, [admin<PERSON><PERSON><PERSON><PERSON>, consultantContext, router]);\n\n  if (!adminContext || !consultantContext) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  const { aToken } = adminContext;\n  const { dToken } = consultantContext;\n\n  if (!aToken && !dToken) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-[#F8F9FD]\">\n      <Navbar />\n      <div className=\"flex items-start\">\n        <Sidebar />\n        <div className=\"flex-1 p-4\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,oBAAoB,IAAA,mNAAU,EAAC,yJAAiB;IACtD,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QAEzC,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,IAAI,CAAC,UAAU,CAAC,QAAQ;YACtB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAc;QAAmB;KAAO;IAE5C,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QACvC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wIAAO;;;;;kCACR,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/app/all-appointments/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext, useEffect } from 'react';\nimport { AdminContext } from '@/context/AdminContext';\nimport { AppContext } from '@/context/AppContext';\nimport { assets } from '@/assets/assets';\nimport DashboardLayout from '@/components/DashboardLayout';\n\nconst AllAppointments: React.FC = () => {\n  const adminContext = useContext(AdminContext);\n  const appContext = useContext(AppContext);\n\n  if (!adminContext || !appContext) {\n    return <div>Loading...</div>;\n  }\n\n  const { aToken, appointments, getAllAppointments, cancelAppointment } = adminContext;\n  const { calculateAge, slotDateFormat, currency } = appContext;\n\n  useEffect(() => {\n    if (aToken) {\n      getAllAppointments();\n    }\n  }, [aToken, getAllAppointments]);\n\n  return (\n    <DashboardLayout>\n      <div className=\"w-full max-w-6xl m-5\">\n        <p className=\"mb-3 text-lg font-medium\">All Appointments</p>\n\n        <div className=\"bg-white border rounded text-sm max-h-[80vh] min-h-[60vh] overflow-y-scroll\">\n          <div className=\"hidden sm:grid grid-cols-[0.5fr_3fr_1fr_3fr_3fr_1fr_1fr] grid-flow-col py-3 px-6 border-b\">\n            <p>#</p>\n            <p>Client</p>\n            <p>Age</p>\n            <p>Date & Time</p>\n            <p>Consultant</p>\n            <p>Fees</p>\n            <p>Actions</p>\n          </div>\n\n          {appointments.map((item, index) => (\n            <div\n              className=\"flex flex-wrap justify-between max-sm:gap-2 sm:grid sm:grid-cols-[0.5fr_3fr_1fr_3fr_3fr_1fr_1fr] items-center text-gray-500 py-3 px-6 border-b hover:bg-gray-50\"\n              key={index}\n            >\n              <p className=\"max-sm:hidden\">{index + 1}</p>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center\">\n                  <span className=\"text-xs font-medium\">\n                    {item.userData.name.charAt(0).toUpperCase()}\n                  </span>\n                </div>\n                <p>{item.userData.name}</p>\n              </div>\n              <p className=\"max-sm:hidden\">{calculateAge(item.userData.dob)}</p>\n              <p>\n                {slotDateFormat(item.slotDate)}, {item.slotTime}\n              </p>\n              <div className=\"flex items-center gap-2\">\n                <img\n                  className=\"w-8 rounded-full bg-gray-200\"\n                  src={item.docData.image}\n                  alt={item.docData.name}\n                />\n                <p>{item.docData.name}</p>\n              </div>\n              <p>\n                {currency}\n                {item.amount}\n              </p>\n              {item.cancelled ? (\n                <p className=\"text-red-400 text-xs font-medium\">Cancelled</p>\n              ) : item.isCompleted ? (\n                <p className=\"text-green-500 text-xs font-medium\">Completed</p>\n              ) : (\n                <img\n                  onClick={() => cancelAppointment(item._id)}\n                  className=\"w-10 cursor-pointer\"\n                  src={assets.cancel_icon}\n                  alt=\"Cancel\"\n                />\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n};\n\nexport default AllAppointments;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,kBAA4B;IAChC,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,aAAa,IAAA,mNAAU,EAAC,2IAAU;IAExC,IAAI,CAAC,gBAAgB,CAAC,YAAY;QAChC,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,GAAG;IACxE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG;IAEnD,IAAA,kNAAS,EAAC;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAmB;IAE/B,qBACE,8OAAC,gJAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAA2B;;;;;;8BAExC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;;;;;;;wBAGJ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;gCACC,WAAU;;kDAGV,8OAAC;wCAAE,WAAU;kDAAiB,QAAQ;;;;;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;0DAG7C,8OAAC;0DAAG,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;kDAExB,8OAAC;wCAAE,WAAU;kDAAiB,aAAa,KAAK,QAAQ,CAAC,GAAG;;;;;;kDAC5D,8OAAC;;4CACE,eAAe,KAAK,QAAQ;4CAAE;4CAAG,KAAK,QAAQ;;;;;;;kDAEjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,KAAK,KAAK,OAAO,CAAC,KAAK;gDACvB,KAAK,KAAK,OAAO,CAAC,IAAI;;;;;;0DAExB,8OAAC;0DAAG,KAAK,OAAO,CAAC,IAAI;;;;;;;;;;;;kDAEvB,8OAAC;;4CACE;4CACA,KAAK,MAAM;;;;;;;oCAEb,KAAK,SAAS,iBACb,8OAAC;wCAAE,WAAU;kDAAmC;;;;;mFAC9C,KAAK,WAAW,iBAClB,8OAAC;wCAAE,WAAU;kDAAqC;;;;;iGAElD,8OAAC;wCACC,SAAS,IAAM,kBAAkB,KAAK,GAAG;wCACzC,WAAU;wCACV,KAAK,iIAAM,CAAC,WAAW;wCACvB,KAAI;;;;;;;+BApCH;;;;;;;;;;;;;;;;;;;;;;AA6CnB;uCAEe", "debugId": null}}]}